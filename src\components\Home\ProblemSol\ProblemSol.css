/* Problem & Solution Component Styles */

/* Custom color variables for this component */
:root {
    --problem-dark: #121214;
    --problem-dark-blue: #16213e;
    --solution-white: #f7f7f2;
    --solution-light: #ffffff;
    --text-dark: #121214;
    --text-light: #f7f7f2;
    --accent-blue: #121214;
    --border-color: #727072;

    /* Mobile-friendly viewport height */
    --mobile-vh: 1vh;
}

/* Set mobile viewport height for better mobile browser support */
@supports (height: 100dvh) {
    :root {
        --mobile-vh: 1dvh;
    }
}

/* Container for the entire Problem & Solution section */
.problem-sol-container {
    position: relative;
    width: 100%;
    min-height: 100vh;
}

/* Background element for seamless color transitions */
.problem-sol-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: calc(var(--mobile-vh, 1vh) * 100);
    z-index: -1;
    background-color: var(--problem-dark);
    will-change: background-color;
    opacity: 0;
}

/* Sticky Titles Section */
.sticky-titles {
    position: relative;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    padding: 2em;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    z-index: 10;
}

.sticky-titles-nav,
.sticky-titles-footer {
    width: 100%;
    display: flex;
    justify-content: space-between;
}

.sticky-titles-nav {
    border-bottom: 1px dashed var(--border-color);
    padding-bottom: 0.5em;
}

.sticky-titles-footer {
    border-top: 1px dashed var(--border-color);
    padding-top: 0.5em;
}

.sticky-titles .primary.sm {
    font-family: 'Rader', sans-serif;
    font-size: 0.9rem;
    color: var(--text-light);
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 500;
    transition: color 0.5s ease;
}

.sticky-titles .primary.sm.solution-phase {
    color: var(--text-dark);
}

.sticky-titles h2 {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    will-change: transform, opacity;
    font-family: 'Rader', sans-serif;
    font-size: clamp(2rem, 5vw, 4rem);
    line-height: 1.2;
    font-weight: 700;
    max-width: 90%;
    margin: 0;
}

.problem-title {
    color: var(--text-light);
}

.solution-title {
    color: var(--text-dark);
}

/* Sticky Work Header Section - Background Text */
.sticky-work-header {
    position: relative;
    width: 100vw;
    height: 100vh;
    padding: 2em;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    overflow: hidden;
    z-index: 5;
    background-color: transparent;
}

.sticky-work-header h1 {
    opacity: 0.35;
    line-height: 0.9;
    font-family: 'Rader', sans-serif;
    font-size: clamp(4rem, 12vw, 16rem);
    font-weight: 900;
    color: var(--text-dark);
    margin: 0;
    text-align: center;
    white-space: nowrap;
    letter-spacing: -0.02em;
}

/* Solutions Section - Scrolling Content */
.solutions-section {
    position: relative;
    width: 100%;
    min-height: 300vh;
    padding: 4em 2em;
    background-color: transparent;
    z-index: 10;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
}

/* Solutions List */
.solutions-list {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: 800px;
    display: flex;
    flex-direction: column;
    gap: 4em;
}

.solution-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 1em;
    padding: 2em;
    border-radius: 1em;
    transition: transform 0.3s ease;
    border: 1px dashed var(--border-color);
    backdrop-filter: blur(3px);
    margin: 2em 0;
}

.solution-item:hover {
    transform: translateY(-5px);
}

.solution-number {
    margin-bottom: 0.5em;
}

.solution-number p {
    font-family: 'Rader', sans-serif;
    font-size: 0.9rem;
    color: var(--border-color);
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 2px;
    font-weight: 500;
}

.solution-content {
    display: flex;
    flex-direction: column;
    gap: 1.5em;
    align-items: center;
    padding: 0;
}

.solution-content h2 {
    font-family: 'Rader', sans-serif;
    font-size: clamp(1.8rem, 4vw, 3rem);
    font-weight: 900;
    color: var(--text-dark);
    margin: 0;
    line-height: 1.1;
    text-align: center;
    letter-spacing: -0.01em;
}

.solution-subtitle {
    font-family: 'Rader', sans-serif;
    font-size: clamp(1rem, 2vw, 1.4rem);
    font-weight: 600;
    color: var(--accent-blue);
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-align: center;
}

.solution-image {
    width: 600px;
    height: 300px;
    border-radius: 0.5em;
    overflow: hidden;
    flex-shrink: 0;
}

.solution-image img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    transition: transform 0.3s ease;
}

/* Responsive Styles */
@media (max-width: 1000px) {
    .sticky-titles {
        padding: 1.25em;
    }

    .sticky-titles h2 {
        width: 90%;
        font-size: clamp(1.5rem, 4vw, 3rem);
    }

    .sticky-work-header {
        padding: 1.25em;
    }

    .sticky-work-header h1 {
        font-size: clamp(3rem, 10vw, 12rem);
    }

    .solutions-section {
        padding: 3em 1.25em;
        min-height: 250vh;
        /* Reduced height for tablets */
    }

    .solutions-list {
        gap: 3em;
    }

    .solution-content h2 {
        font-size: clamp(1.5rem, 3.5vw, 2.5rem);
    }

    .solution-subtitle {
        font-size: clamp(0.9rem, 1.8vw, 1.2rem);
    }

    .solution-image {
        width: 500px;
        height: 400px;
    }
}

@media (max-width: 768px) {
    .sticky-titles {
        padding: 1em;
    }

    .sticky-titles-nav,
    .sticky-titles-footer {
        padding: 0 0.5em;
        gap: 1em;
    }

    .sticky-titles .primary.sm {
        font-size: 1rem;
        letter-spacing: 0.5px;
        flex: 1;
        padding: 0 0.25em;

    }

    .sticky-titles h2 {
        font-size: clamp(1.2rem, 3.5vw, 2.5rem);
    }

    .sticky-work-header {
        padding: 1em;
    }

    .sticky-work-header h1 {
        font-size: clamp(2.5rem, 8vw, 8rem);
        opacity: 0.15;
    }

    .solutions-section {
        padding: 2em 1em;
        min-height: 200vh;
        /* Reduced height for mobile */
    }

    .solutions-list {
        gap: 2.5em;
    }

    .solution-item {
        padding: 1.5em;
    }

    .solution-content h2 {
        font-size: clamp(1.3rem, 3vw, 2rem);
    }

    .solution-subtitle {
        font-size: clamp(0.8rem, 1.5vw, 1rem);
    }

    .solution-image {
        width: 100%;
        max-width: 400px;
        height: 300px;
    }

    .solution-number p {
        font-size: 0.8rem;
    }
}

/* Mobile specific adjustments for small screens like iPhone 12 Pro */
@media (max-width: 480px) {

    .sticky-titles-nav {
        padding-bottom: 10px;
        padding-top: 5px;
        gap: 0.5em;
        justify-content: space-between;
    }

    .sticky-titles-footer {
        padding-top: 20px;
        padding-bottom: 10px;
        gap: 0em;
        justify-content: space-between;
    }

    .sticky-titles .primary.sm {
        font-size: 0.65rem;
        letter-spacing: 0.3px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 45%;
        text-align: center;
    }


    .sticky-titles h2 {
        font-size: clamp(2rem, 4vw, 2rem);
        max-width: 95%;
    }

    .sticky-work-header h1 {
        font-size: clamp(3rem, 6vw, 6rem);
        opacity: 0.12;
    }

    .solutions-section {
        padding: 1.5em 0.5em;
        min-height: 150vh;
        /* Significantly reduced height for small mobile */
    }

    .solution-item {
        padding: 1em;
        margin: 1em 0;
    }

    .solution-content h2 {
        font-size: clamp(1.1rem, 2.8vw, 1.8rem);
    }

    .solution-subtitle {
        font-size: clamp(0.7rem, 1.3vw, 0.9rem);
    }

    .solution-image {
        width: 100%;
        max-width: 320px;
        height: 250px;
    }
}