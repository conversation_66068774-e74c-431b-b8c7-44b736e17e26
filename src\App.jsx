import './App.css'
import Preloader from './components/Preloader/Preloader'
import Home from './components/Home/Home'
import TransitionOverlays from './components/Transition/TransitionOverlays'
import { useState } from 'react'
import { gsap } from 'gsap'
import { <PERSON>rowserRouter as Router } from 'react-router-dom'

function App() {
  const [showPreloader, setShowPreloader] = useState(true);
  const [showHome, setShowHome] = useState(false);

  const handlePreloaderComplete = () => {
    // Start the reveal transition
    revealTransition();
  };

  const revealTransition = () => {
    // First show the home page (but it will be hidden behind overlays)
    setShowHome(true);

    // Hide preloader
    setShowPreloader(false);

    // Set overlays to cover the screen
    gsap.set(".transition-overlay", { scaleY: 1, transformOrigin: "top" });

    // Then animate them sliding up to reveal home page
    gsap.to(".transition-overlay", {
      scaleY: 0,
      duration: 0.6,
      stagger: -0.1,
      ease: "power2.inOut"
    });
  };

  return (
    <Router>
      <div className="App">
        {/* Transition Overlays - Disabled */}
        <TransitionOverlays />

        {/* Show Preloader - Disabled */}
        {showPreloader && (
          <Preloader onComplete={handlePreloaderComplete} />
        )}

        {/* Show Home Page directly */}
        <Home />
      </div>
    </Router>
  )
}

export default App
