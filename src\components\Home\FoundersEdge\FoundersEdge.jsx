import React, { useRef } from 'react';
import { useGSAP } from '@gsap/react';
import { gsap } from 'gsap';
import './FoundersEdge.css';

const FoundersEdge = () => {
  const containerRef = useRef(null);

  // Founders data - exactly matching the HTML example
  const foundersData = [
    {
      title: 'Dr.',
      firstName: 'Sarthak',
      lastName: '<PERSON>',
      img1: '/FounderTeam/img1.png',
      img2: '/FounderTeam/img1_2.png'
    },
    {
      title: 'Dr.',
      firstName: 'ThanSingh',
      lastName: 'Kundra',
      img1: '/FounderTeam/img2.png',
      img2: '/FounderTeam/img2_2.png'
    }
  ];

  // Team/Developers data
  const teamData = [
    {
      title: 'Dev.',
      firstName: 'Har',
      lastName: 'sh',
      img1: '/FounderTeam/img3.png',
      img2: '/FounderTeam/img3_2.png'
    },
    {
      title: '',
      firstName: 'Utka',
      lastName: 'rsh',
      img1: '/FounderTeam/img3.png',
      img2: '/FounderTeam/img3_2.png'

    },
  ];

  // Helper function to create spans for each character - exactly like HTML
  const createCharSpans = (text) => {
    return text.split('').map((char, index) => (
      <span key={index} className="char">
        {char}
      </span>
    ));
  };

  // GSAP Animation Logic - converted from HTML example with mobile support
  useGSAP(() => {
    if (!containerRef.current) return;

    // Add a small delay to ensure DOM is fully rendered
    const timer = setTimeout(() => {
      const memberCards = containerRef.current.querySelectorAll('.member-card');
      const eventListeners = []; // Track event listeners for cleanup

      memberCards.forEach(member => {
        // We select all characters, including the "Dr." title - works for both desktop and mobile
        const chars = member.querySelectorAll('.char');

        // Animation on hover/touch - same for all screen sizes
        const handleInteractionStart = () => {
          if (chars.length > 0) {
            gsap.to(chars, {
              y: -30, // Move letters up
              ease: 'elastic.out(1, 0.3)', // The signature bounce effect
              duration: 0.8,
              stagger: {
                from: 'random', // Animate letters randomly
                amount: 0.2     // Total time for all letters to animate
              }
            });
          }
        };

        // Animation on mouse leave/touch end - same for all screen sizes
        const handleInteractionEnd = () => {
          if (chars.length > 0) {
            gsap.to(chars, {
              y: 0, // Return letters to original position
              ease: 'elastic.out(1, 0.3)',
              duration: 0.8,
              stagger: {
                from: 'random',
                amount: 0.2
              }
            });
          }
        };

        // Add event listeners for both mouse and touch
        member.addEventListener('mouseenter', handleInteractionStart);
        member.addEventListener('mouseleave', handleInteractionEnd);
        member.addEventListener('touchstart', handleInteractionStart);
        member.addEventListener('touchend', handleInteractionEnd);

        // Store for cleanup
        eventListeners.push({
          element: member,
          startHandler: handleInteractionStart,
          endHandler: handleInteractionEnd
        });
      });

      // Cleanup function - properly remove all event listeners
      return () => {
        clearTimeout(timer);
        eventListeners.forEach(({ element, startHandler, endHandler }) => {
          element.removeEventListener('mouseenter', startHandler);
          element.removeEventListener('mouseleave', endHandler);
          element.removeEventListener('touchstart', startHandler);
          element.removeEventListener('touchend', endHandler);
        });
      };
    }, 100);
  }, { scope: containerRef });

  return (
    <section className="founders-edge-section">
      <div className="founders-edge-container" ref={containerRef}>
        {/* Header positioned on the left side - matching theme pattern */}
        <div className="founders-edge-header">
          <h1>✦ FOUNDER'S EDGE </h1>
        </div>

        {/* Founders Container - centered within dashed border */}
        <div className="founders-names-layout">
          {foundersData.map((founder, index) => (
            <div key={`founder-${index}`} className="member-card">
              {/* Name for large screens (split with image) - exact HTML structure */}
              <div className="member-name-lg">
                <div className="dr-title">
                  {createCharSpans(founder.title.toUpperCase())}
                </div>
                <div className="name-part-1">
                  {createCharSpans(founder.firstName.toUpperCase())}
                </div>
                <div className="member-image-container">
                  <img
                    src={founder.img1}
                    alt={founder.firstName}
                    className="member-image default"
                  />
                  <img
                    src={founder.img2}
                    alt={`${founder.firstName} hover`}
                    className="member-image hover"
                  />
                </div>
                <div className="name-part-2">
                  {createCharSpans(founder.lastName.toUpperCase())}
                </div>
              </div>

              {/* Mobile layout - image on top, name below */}
              <div className="member-mobile-layout">
                <div className="member-mobile-image">
                  <img
                    src={founder.img1}
                    alt={founder.firstName}
                    className="member-image default"
                  />
                  <img
                    src={founder.img2}
                    alt={`${founder.firstName} hover`}
                    className="member-image hover"
                  />
                </div>
                <div className="member-mobile-name">
                  <span className="mobile-title">
                    {createCharSpans((founder.title || '').toUpperCase())}
                  </span>
                  <span className="mobile-name">
                    {createCharSpans(founder.firstName.toUpperCase())}
                  </span>
                  <span className="mobile-name">
                    {createCharSpans(founder.lastName.toUpperCase())}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Second Header for AIR-HS Team */}
        <div className="team-edge-header">
          <h1>✦ AIR-HS TEAM </h1>
        </div>

        {/* Team/Developers Container - centered within dashed border */}
        <div className="team-names-layout">
          {teamData.map((member, index) => (
            <div key={`team-${index}`} className="member-card">
              {/* Name for large screens (split with image) - exact HTML structure */}
              <div className="member-name-lg">
                <div className="dr-title">
                  {createCharSpans((member.title || '').toUpperCase())}
                </div>
                <div className="name-part-1">
                  {createCharSpans(member.firstName.toUpperCase())}
                </div>
                <div className="member-image-container">
                  <img
                    src={member.img1}
                    alt={member.firstName}
                    className="member-image default"
                  />
                  <img
                    src={member.img2}
                    alt={`${member.firstName} hover`}
                    className="member-image hover"
                  />
                </div>
                <div className="name-part-2">
                  {createCharSpans(member.lastName.toUpperCase())}
                </div>
              </div>

              {/* Mobile layout - image on top, name below */}
              <div className="member-mobile-layout">
                <div className="member-mobile-image">
                  <img
                    src={member.img1}
                    alt={member.firstName}
                    className="member-image default"
                  />
                  <img
                    src={member.img2}
                    alt={`${member.firstName} hover`}
                    className="member-image hover"
                  />
                </div>
                <div className="member-mobile-name">
                  <span className="mobile-title">
                    {createCharSpans((member.title || '').toUpperCase())}
                  </span>
                  <span className="mobile-name">
                    {createCharSpans(member.firstName.toUpperCase())}
                  </span>
                  <span className="mobile-name">
                    {createCharSpans(member.lastName.toUpperCase())}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FoundersEdge;