.loader-counter-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.loader-counter {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 100;
}

.loader-count {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  z-index: 100;
}

.loader-digit {
  flex: 1;
  padding-top: 1rem;
  clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
  overflow: hidden;
}

.loader-count .loader-digit h1 {
  color: #828fd7;
  /* Using your project's primary color */
  font-family: 'Rader', monospace;
  font-size: 3rem;
  font-weight: 700;
  margin: 0;
  line-height: 1;
  position: relative;
  transform: translateY(120%);
  will-change: transform;
}

.loader-progress-bar {
  color: #828fd7;
  background-color: #828fd7;
  /* Styles are applied inline via JavaScript for flexibility */
}

/* Mobile responsive styles */
@media (max-width: 768px) {
  .loader-count .loader-digit h1 {
    font-size: 2rem;
  }

  .loader-digit {
    padding-top: 0.5rem;
  }
}

@media (max-width: 480px) {
  .loader-count .loader-digit h1 {
    font-size: 1.5rem;
  }

  .loader-digit {
    padding-top: 0.3rem;
  }
}

@media (max-width: 320px) {
  .loader-count .loader-digit h1 {
    font-size: 1.2rem;
  }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .loader-count .loader-digit h1 {
    color: #828fd7;
    /* Keep the same color for consistency */
  }
}

/* Light theme support */
@media (prefers-color-scheme: light) {
  .loader-count .loader-digit h1 {
    color: #666666;
    /* Darker color for light theme */
  }
}