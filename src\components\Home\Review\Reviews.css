.reviews {
    position: relative;
    width: 100vw;
    height: 100svh;
    background-color: var(--fg);
    color: var(--bg);
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    gap: 2em;
}

.review-box {
    position: relative;
    width: 97vw;
    height: 90svh;
    background-color: var(--fg);
    color: var(--bg);
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border: dashed 1px #ffffff;
    gap: 2em;
    border-radius: 50px;
}

#review-copy {
    width: 70%;
    margin: 0 auto;
    padding: 0 1rem;
}

.review-item {
    width: 100%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    display: flex;
    flex-direction: column;
    gap: 2em;

    font-size: 2rem;
    font-style: italic;
}

#quote-icon {
    position: absolute;
    top: 20%;
    left: 50%;
    transform: translateX(-50%);
    font-size: 2.2rem;
    color: var(--bg200);
}

.reviews-list {
    position: absolute;
    bottom: 10%;
    width: 35%;
    margin: 0 auto;
    display: flex;
    gap: 1.5rem;
    justify-content: center;
}

.review-thumbnail {
    aspect-ratio: 1/1;
    min-height: 50px;
    border-radius: 0.5em;
    overflow: hidden;
    cursor: pointer;
    transition: border 0.3s ease-in-out;
}

.review-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: fill;
    object-position: bottom;
    border-radius: 0.5em;

}

.review-thumbnail.active {
    padding: 4px;
    border: 1px dashed var(--bg);
    border-radius: 0.5em;
}

h4#review-copy,
h4#review-author {
    font-kerning: none;

    overflow: hidden;
    line-height: 1 !important;
}

h4#review-copy .line,
h4#review-author .line {
    position: relative;
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
    overflow: hidden;
}

h4#review-copy .line span,
h4#review-author .line span {
    position: relative;
    display: inline-block;
    will-change: transform;
}

/* Trusted Hospitals Section */
.trusted-hospitals {
    width: 100vw;
    margin-top: 1em;
    padding: 2em;
    padding-top: 8em;
    padding-bottom: 0;
    background-color: #121214;
    display: flex;
    flex-direction: column;
    gap: 2em;
}

.trusted-hospitals-header {
    text-align: center;
}

.trusted-hospitals-header h2 {
    font-size: clamp(2rem, 4vw, 3rem);
    color: white;
    font-weight: bold;
    margin: 0px;
    margin-bottom: 40px;
    font-style: italic;
}

.hospitals-grid {
    display: flex;
    gap: 1em;
    width: 100%;
}

.hospital {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    aspect-ratio: 1;
    border: 1px dashed white;
    border-radius: 1em;
    background-color: transparent;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
}

.hospital::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: var(--bg-image);
    background-size: cover;
    background-position: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.hospital.hovered::before {
    opacity: 1;
}

.hospital-name {
    font-size: clamp(0.9rem, 1.5vw, 1.1rem);
    color: white;
    font-weight: bold;
    margin: 0;
    line-height: 1.2;
    letter-spacing: 0.5px;
    position: relative;
    z-index: 2;
    transition: color 0.3s ease;
}

.hospital:hover .hospital-name {
    color: white;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

/* Animation Classes for GSAP */
.hospital-name {
    will-change: transform, opacity;
}

/* Tablet responsive styles */
@media (max-width: 1000px) {
    .trusted-hospitals {
        padding: 1.25em;
        padding-bottom: 0;
        gap: 1.25em;
    }

    .hospitals-grid {
        flex-direction: column;
        gap: 1.25em;
    }

    .hospital {
        aspect-ratio: 5/3;
    }

    .hospital-name {
        font-size: clamp(1rem, 2vw, 1.2rem);
    }

    .reviews {
        padding: 2em 1em;
    }

    #review-copy {
        width: 85%;
        padding: 0 0.5rem;
    }

    .reviews-list {
        width: 60%;
        gap: 1rem;
    }

    .review-item {
        padding: 1em;
        font-size: 1.8rem;
    }

    #quote-icon {
        top: 15%;
        font-size: 2rem;
    }
}

/* Mobile responsive styles */
@media (max-width: 768px) {
    .reviews {
        padding: 1.5em 0.8em;
        gap: 1.5em;
    }

    .review-box {
        width: 95vw;
        height: 85svh;
        border-radius: 30px;
        padding: 1rem;
    }

    #review-copy {
        width: 90%;
        padding: 0 0.5rem;
    }

    .review-item {
        font-size: 1.5rem;
        gap: 1.5em;
        padding: 0.8em;
    }

    .reviews-list {
        width: 70%;
        bottom: 8%;
        gap: 0.8rem;
    }

    .review-thumbnail {
        min-height: 45px;
    }

    #quote-icon {
        top: 12%;
        font-size: 1.8rem;
    }
}

/* Small mobile responsive styles */
@media (max-width: 480px) {
    .reviews {
        padding: 1em 0.5em;
        gap: 1em;
    }

    .review-box {
        width: 92vw;
        height: 80svh;
        border-radius: 20px;
        padding: 0.8rem;
    }

    #review-copy {
        width: 95%;
        padding: 0 0.3rem;
    }

    .review-item {
        font-size: 1.2rem;
        gap: 1.2em;
        padding: 0.6em;
    }

    .reviews-list {
        width: 80%;
        bottom: 6%;
        gap: 0.6rem;
    }

    .review-thumbnail {
        min-height: 40px;
    }

    #quote-icon {
        top: 10%;
        font-size: 1.5rem;
    }
}

/* Very small mobile responsive styles */
@media (max-width: 390px) {
    .reviews {
        padding: 0.8em 0.3em;
    }

    .review-box {
        width: 90vw;
        height: 75svh;
        border-radius: 15px;
        padding: 0.6rem;
    }

    #review-copy {
        width: 98%;
        padding: 0 0.2rem;
    }

    .review-item {
        font-size: 1rem;
        gap: 1em;
        padding: 0.5em;
    }

    .reviews-list {
        width: 85%;
        bottom: 5%;
        gap: 0.5rem;
    }

    .review-thumbnail {
        min-height: 35px;
    }

    #quote-icon {
        top: 8%;
        font-size: 1.3rem;
    }
}