.contact-form {
    padding: 2em;
    display: flex;
    flex-direction: column;
    gap: 4em;
    margin: 2em;
    background-color: #0f0f0f;
    border-radius: 1em;
}

.contact-form h3 {
    color: #e3e3db;
    font-size: clamp(2rem, 8vw, 5rem);
    font-weight: 600;
    margin: 0;
    line-height: 1.1;
}

.contact-form p,
.contact-form input,
.contact-form textarea {
    color: #e3e3db;
}

.contact-form input,
.contact-form textarea {
    background-color: #1f1f1f;
    border: 1px solid #333;
    border-radius: 0.5em;
    padding: 1em;
    font-size: 1rem;
    font-family: inherit;
    transition: border-color 0.3s ease;
    width: 100%;
}

.contact-form input:focus,
.contact-form textarea:focus {
    outline: none;
    border-color: #e3e3db;
}

.contact-form input::placeholder,
.contact-form textarea::placeholder {
    color: #888;
}

.contact-form textarea {
    resize: vertical;
    min-height: 120px;
}

.contact-form .btn {
    background-color: #e3e3db;
    color: #0f0f0f;
    border: none;
    border-radius: 0.5em;
    padding: 1em 2em;
    font-size: 1rem;
    font-family: inherit;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.contact-form .btn:hover {
    background-color: #d4d4c6;
    transform: translateY(-1px);
}

.contact-form-row:nth-child(1) {
    width: 100%;
    display: flex;
    gap: 1em;
}

.contact-form-row-copy-item {
    flex: 1;
}

.contact-form-row-copy-item p {
    font-size: 1.2rem;
}

.contact-form-row-copy-item:nth-child(2) {
    text-align: center;
}

.contact-form-row-copy-item:nth-child(3) {
    text-align: right;
}

.contact-form-col:nth-child(1) {
    display: flex;
    flex-direction: column;
    gap: 1.5em;
}

.contact-form-header {
    display: flex;
    flex-direction: column;
    gap: 1.5em;
}

.contact-form-header p {
    margin-bottom: 0.5em;
    width: 75%;
}

.contact-form-row:nth-child(2) {
    display: flex;
    gap: 1em;
}

.contact-form-col {
    flex: 1;
}

.contact-form-col:nth-child(1) {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 4em;
}

.contact-form-availability {
    width: 75%;
    display: flex;
    justify-content: space-between;
    gap: 1em;
    border-top: 1px dashed #e3e3db;
    padding: 0.5em 0;
}

.contact-form-col:nth-child(2) {
    display: flex;
    flex-direction: column;
    gap: 0.75em;
}

.form-item .btn {
    width: 100%;
}

/* Tablet responsive styles */
@media (max-width: 900px) {
    .contact-form {
        margin: 1.25em;
        padding: 1.5em;
        gap: 3em;
    }

    .contact-form h3 {
        font-size: clamp(1.8rem, 6vw, 3.5rem);
    }

    .contact-form-row:nth-child(1) {
        flex-direction: column;
        gap: 0.25em;
    }

    .contact-form-row-copy-item {
        text-align: center;
    }

    .contact-form-row-copy-item:nth-child(3),
    .contact-form h3,
    .contact-form p {
        text-align: center;
    }

    .contact-form-row:nth-child(2) {
        flex-direction: column;
        gap: 3em;
    }

    .contact-form-header p,
    .contact-form-availability {
        width: 100%;
    }

    .contact-form-col:nth-child(1) {
        gap: 2em;
    }

    .contact-form-availability {
        flex-direction: column;
        text-align: center;
        gap: 0.25em;
    }
}

/* Mobile responsive styles */
@media (max-width: 768px) {
    .contact-form {
        margin: 1em;
        padding: 1.2em;
        gap: 2.5em;
    }

    .contact-form h3 {
        font-size: clamp(1.5rem, 5vw, 2.5rem);
        line-height: 1.2;
    }

    .contact-form-row-copy-item p {
        font-size: 1rem;
    }

    .contact-form input,
    .contact-form textarea {
        padding: 0.8em;
        font-size: 0.9rem;
    }

    .contact-form textarea {
        min-height: 100px;
    }

    .contact-form .btn {
        padding: 0.8em 1.5em;
        font-size: 0.9rem;
    }

    .contact-form-row:nth-child(2) {
        gap: 2.5em;
    }

    .contact-form-col:nth-child(1) {
        gap: 1.5em;
    }
}

/* Small mobile responsive styles */
@media (max-width: 480px) {
    .contact-form {
        margin: 0.8em;
        padding: 1em;
        gap: 2em;
    }

    .contact-form h3 {
        font-size: clamp(1.3rem, 4vw, 2rem);
        line-height: 1.3;
    }

    .contact-form-header p {
        font-size: 0.9rem;
        width: 100%;
    }

    .contact-form-row-copy-item p {
        font-size: 0.9rem;
    }

    .contact-form input,
    .contact-form textarea {
        padding: 0.7em;
        font-size: 0.85rem;
    }

    .contact-form textarea {
        min-height: 80px;
    }

    .contact-form .btn {
        padding: 0.7em 1.2em;
        font-size: 0.85rem;
    }

    .contact-form-row:nth-child(2) {
        gap: 2em;
    }

    .contact-form-col:nth-child(1) {
        gap: 1.2em;
    }

    .contact-form-availability {
        gap: 0.5em;
        padding: 0.8em 0;
    }
}

/* Very small mobile responsive styles */
@media (max-width: 390px) {
    .contact-form {
        margin: 0.5em;
        padding: 0.8em;
        gap: 1.5em;
    }

    .contact-form h3 {
        font-size: clamp(1.1rem, 3.5vw, 1.8rem);
        line-height: 1.4;
    }

    .contact-form-header p {
        font-size: 0.8rem;
    }

    .contact-form-row-copy-item p {
        font-size: 0.8rem;
    }

    .contact-form input,
    .contact-form textarea {
        padding: 0.6em;
        font-size: 0.8rem;
    }

    .contact-form textarea {
        min-height: 70px;
    }

    .contact-form .btn {
        padding: 0.6em 1em;
        font-size: 0.8rem;
    }

    .contact-form-row:nth-child(2) {
        gap: 1.5em;
    }

    .contact-form-col:nth-child(1) {
        gap: 1em;
    }

    .contact-form-availability {
        gap: 0.3em;
        padding: 0.6em 0;
        font-size: 0.8rem;
    }
}